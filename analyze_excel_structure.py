#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析Excel文件结构，提取宿舍和部门信息
"""

from openpyxl import load_workbook

def analyze_excel_data():
    """分析Excel文件数据结构"""
    wb = load_workbook("新建 XLSX 工作表.xlsx")
    sheet = wb["Sheet1"]
    
    print("=== Excel文件数据分析 ===")
    print(f"总行数: {sheet.max_row}")
    print(f"总列数: {sheet.max_column}")
    
    # 显示列标题
    headers = []
    for col in range(1, sheet.max_column + 1):
        header = sheet.cell(row=1, column=col).value
        headers.append(str(header) if header is not None else f"列{col}")
    print(f"列标题: {headers}")
    
    # 分析数据
    departments = set()
    dormitories = set()
    
    print("\n=== 前10行数据示例 ===")
    for row in range(1, min(11, sheet.max_row + 1)):
        row_data = []
        for col in range(1, sheet.max_column + 1):
            cell_value = sheet.cell(row=row, column=col).value
            row_data.append(str(cell_value) if cell_value is not None else "")
        print(f"第{row}行: {row_data}")
        
        # 从第2行开始提取数据
        if row > 1:
            # 提取部门信息（第5列）
            dept_value = sheet.cell(row=row, column=5).value
            if dept_value is not None:
                department = str(dept_value).strip()
                if department:
                    departments.add(department)
            
            # 提取宿舍信息（第1列）
            dorm_value = sheet.cell(row=row, column=1).value
            if dorm_value is not None:
                dormitory = str(dorm_value).strip()
                if dormitory and dormitory != "":
                    dormitories.add(dormitory)
    
    print(f"\n=== 提取的部门信息 ===")
    print(f"部门数量: {len(departments)}")
    for i, dept in enumerate(sorted(departments), 1):
        print(f"{i}. {dept}")
    
    print(f"\n=== 提取的宿舍信息 ===")
    print(f"宿舍数量: {len(dormitories)}")
    for i, dorm in enumerate(sorted(dormitories), 1):
        print(f"{i}. {dorm}")
    
    wb.close()
    return departments, dormitories

if __name__ == "__main__":
    analyze_excel_data()
