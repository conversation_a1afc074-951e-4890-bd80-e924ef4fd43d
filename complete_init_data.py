#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的数据初始化脚本
根据Excel文件解析数据并通过API初始化部门、宿舍和住户信息
"""

from openpyxl import load_workbook
import requests
import json
from datetime import datetime
import re

# API配置
BASE_URL = "http://localhost:8005/api/v1"

def clean_dormitory_name(name):
    """清理宿舍名称，去除多余的空格和格式化"""
    if not name:
        return ""
    # 去除多余的空格
    cleaned = re.sub(r'\s+', ' ', str(name).strip())
    return cleaned

def parse_excel_data(file_path):
    """解析Excel文件，提取所有需要的数据"""
    wb = load_workbook(file_path)
    sheet = wb["Sheet1"]
    
    departments = set()
    dormitories = set()
    residents_data = []
    
    print("=== 开始解析Excel数据 ===")
    
    # 从第2行开始读取数据（第1行是标题）
    for row in range(2, sheet.max_row + 1):
        # 提取各列数据
        dormitory_raw = sheet.cell(row=row, column=1).value  # 宿舍名称
        bed_number = sheet.cell(row=row, column=2).value     # 床号
        resident_name = sheet.cell(row=row, column=3).value  # 住户姓名
        check_in_date = sheet.cell(row=row, column=4).value  # 入住时间
        department = sheet.cell(row=row, column=5).value     # 部门
        client = sheet.cell(row=row, column=6).value         # 客户
        cost_ratio = sheet.cell(row=row, column=7).value     # 报销分摊比例
        contract_no = sheet.cell(row=row, column=8).value    # 合同编号
        
        # 处理部门信息
        if department is not None:
            dept_name = str(department).strip()
            if dept_name:
                departments.add(dept_name)
        
        # 处理宿舍信息
        if dormitory_raw is not None:
            dorm_name = clean_dormitory_name(dormitory_raw)
            if dorm_name:
                dormitories.add(dorm_name)
        
        # 处理住户信息
        if resident_name is not None:
            resident_info = {
                'name': str(resident_name).strip(),
                'bed_number': str(bed_number) if bed_number else '',
                'department': str(department).strip() if department else '',
                'dormitory': clean_dormitory_name(dormitory_raw) if dormitory_raw else '',
                'check_in_date': str(check_in_date) if check_in_date else '',
                'client': str(client).strip() if client else '',
                'cost_ratio': str(cost_ratio).strip() if cost_ratio else '',
                'contract_no': str(contract_no).strip() if contract_no else ''
            }
            residents_data.append(resident_info)
    
    wb.close()
    
    print(f"解析完成:")
    print(f"- 部门数量: {len(departments)}")
    print(f"- 宿舍数量: {len(dormitories)}")
    print(f"- 住户数量: {len(residents_data)}")
    
    return departments, dormitories, residents_data

def create_departments(departments):
    """创建部门"""
    print("\n=== 开始创建部门 ===")
    created_departments = {}
    
    for department in sorted(departments):
        try:
            data = {
                "name": department,
                "description": f"从Excel导入的部门: {department}",
                "is_active": True
            }
            
            response = requests.post(f"{BASE_URL}/departments/create", json=data)
            
            if response.status_code == 201:
                dept_info = response.json()
                created_departments[department] = dept_info['id']
                print(f"✓ 创建部门成功: {department} (ID: {dept_info['id']})")
            else:
                print(f"✗ 创建部门失败: {department} - {response.status_code}: {response.text}")
                
        except Exception as e:
            print(f"✗ 创建部门异常: {department} - {str(e)}")
    
    return created_departments

def create_dormitories(dormitories, department_mapping):
    """创建宿舍"""
    print("\n=== 开始创建宿舍 ===")
    created_dormitories = {}
    
    for dormitory in sorted(dormitories):
        try:
            # 根据宿舍名称估算床位数，这里简单设置为20个床位
            # 实际项目中可能需要更复杂的逻辑来确定床位数
            total_beds = 20
            
            data = {
                "name": dormitory,
                "total_beds": total_beds,
                "description": f"从Excel导入的宿舍: {dormitory}",
                "is_active": True
            }
            
            response = requests.post(f"{BASE_URL}/dormitories/create", json=data)
            
            if response.status_code == 201:
                dorm_info = response.json()
                created_dormitories[dormitory] = dorm_info['id']
                print(f"✓ 创建宿舍成功: {dormitory[:50]}... (ID: {dorm_info['id']})")
            else:
                print(f"✗ 创建宿舍失败: {dormitory[:50]}... - {response.status_code}: {response.text}")
                
        except Exception as e:
            print(f"✗ 创建宿舍异常: {dormitory[:50]}... - {str(e)}")
    
    return created_dormitories

def create_residents(residents_data, department_mapping, dormitory_mapping):
    """创建住户信息"""
    print("\n=== 开始创建住户 ===")
    created_residents = {}

    for i, resident in enumerate(residents_data, 1):
        try:
            # 获取部门ID
            department_id = department_mapping.get(resident['department'])

            if not department_id:
                print(f"✗ 跳过住户 {resident['name']}: 找不到部门 '{resident['department']}'")
                continue

            data = {
                "name": resident['name'],
                "department_id": department_id,
                "employee_id": None,  # Excel中没有员工号信息
                "phone": None,        # Excel中没有电话信息
                "email": None,        # Excel中没有邮箱信息
                "is_active": True
            }

            response = requests.post(f"{BASE_URL}/residents/create", json=data)

            if response.status_code == 200:
                resident_info = response.json()
                created_residents[resident['name']] = resident_info['id']
                print(f"✓ 创建住户成功: {resident['name']} - 部门: {resident['department']}")
            else:
                print(f"✗ 创建住户失败: {resident['name']} - {response.status_code}: {response.text}")

        except Exception as e:
            print(f"✗ 创建住户异常: {resident['name']} - {str(e)}")

    return created_residents

def main():
    """主函数"""
    file_path = "新建 XLSX 工作表.xlsx"
    
    try:
        # 1. 解析Excel数据
        departments, dormitories, residents_data = parse_excel_data(file_path)
        
        # 2. 创建部门
        department_mapping = create_departments(departments)
        
        # 3. 创建宿舍
        dormitory_mapping = create_dormitories(dormitories, department_mapping)
        
        # 4. 创建住户
        resident_mapping = create_residents(residents_data, department_mapping, dormitory_mapping)

        print("\n=== 数据初始化完成 ===")
        print(f"成功创建部门: {len(department_mapping)}")
        print(f"成功创建宿舍: {len(dormitory_mapping)}")
        print(f"成功创建住户: {len(resident_mapping)}")
        print(f"总住户数据: {len(residents_data)}")
        
    except Exception as e:
        print(f"数据初始化失败: {str(e)}")

if __name__ == "__main__":
    main()
