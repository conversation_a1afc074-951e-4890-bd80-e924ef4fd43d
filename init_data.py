
from openpyxl import load_workbook
import requests

url ="http://localhost:8005/api/v1"


# 加载工作簿
wb = load_workbook("新建 XLSX 工作表.xlsx")

# 选择表
sheet = wb["Sheet1"]

departments = set()  # 使用集合自动去重
dormitories = set()

# 从第2行开始读取（第1行是标题）
for row in range(2, sheet.max_row + 1):
    cell_value1 = sheet.cell(row=row, column=5).value
    if cell_value1 is not None:
        department = str(cell_value1).strip()
        if department:
            departments.add(department)

    # cell_value2 = sheet.cell(row=row, column=1).value
    # if cell_value2 is not None:
    #     dormitories = str(cell_value2).strip()
    #     if dormitories:
    #         dormitories.add(dormitories)

headers = {"Authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ3YW5nemhpeGluIiwidXNlcm5hbWUiOiJ3YW5nemhpeGluIiwiZXhwIjoxNzU0ODk0Nzc0LCJpYXQiOjE3NTQ4OTI5NzQsInR5cGUiOiJhY2Nlc3MifQ.5-6TXlEw89MukTI2F97p2qEiu09r562yzEvtIaOKrwo"}

# 添加部门
if departments:
    for department in departments:
        data = {"name": department}
        response = requests.post(url + "/departments/create", json=data, headers = headers)
        print(f"添加部门 {department}: {response.status_code}")


# #添加宿舍
# if dormitories:
#     for dormitory in dormitories:
#         data = {"name": dormitory, "total_beds": 10,}
#         response = requests.post(url + "/dormitories/create", json=data)
#         print(f"添加宿舍 {dormitory}: {response.status_code}")


wb.close()
